import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  TextField,
  Typography,
  ListItemText,
  Box,
  Autocomplete,
  ListItem,
} from "@mui/material";
import { CalendarMonth, MailOutline, Add } from "@mui/icons-material";
import { useMeetingTemplates, SkinnyMeetingDefinition } from "src/queries";
import { INVITE_STYLE } from "src/meetingTypes/invite/props";
import { useDebounce } from "src/hooks";
import { TemplateStatus } from "src/types";

interface FollowUpTemplateDropdownProps {
  value?: SkinnyMeetingDefinition | null;
  onChange: (template: SkinnyMeetingDefinition | null) => void;
  disabled?: boolean;
  onCreateNewTemplate?: () => void;
  refreshTrigger?: number; // Optional prop to force refresh
}

export function FollowUpTemplateDropdown({
  value,
  onChange,
  disabled = false,
  onCreateNewTemplate,
  refreshTrigger,
}: FollowUpTemplateDropdownProps) {
  const [input, setInput] = useState("");

  const query = useDebounce(input, 750);

  const queryOptions = useMemo(
    () => ({
      templateName: query || undefined,
      inviteStyle: [INVITE_STYLE.CALENDAR_FIRST, INVITE_STYLE.CUSTOM_INVITE],
      status: ["active" as TemplateStatus],
    }),
    [query],
  );

  const { data, error, mutate } = useMeetingTemplates(10, 0, queryOptions);

  useEffect(() => {
    if (refreshTrigger) {
      mutate();
    }
  }, [refreshTrigger, mutate]);

  const createNewOption = useMemo(
    (): SkinnyMeetingDefinition => ({
      id: -1, // Use a special ID that won't conflict with real templates
      name: "+ Create New Template",
      inviteStyle: "custom",
      active: true,
      creationData: {
        creatorData: {
          userId: 0,
          userEmail: "",
          userFirstName: "",
          userLastName: "",
          userRole: 0,
          adminEmail: "",
          adminId: 0,
        },
        createdAt: new Date().toISOString(),
      },
      schedulingUrls: null,
    }),
    [],
  );

  const templates = useMemo(
    () =>
      (data?.data || [])
        .slice()
        .sort(
          (a, b) =>
            new Date(b.creationData?.createdAt ?? 0).getTime() -
            new Date(a.creationData?.createdAt ?? 0).getTime(),
        ),
    [data?.data],
  );

  const options = useMemo(() => {
    return onCreateNewTemplate ? [createNewOption, ...templates] : templates;
  }, [onCreateNewTemplate, createNewOption, templates]);

  const handleChange = useCallback(
    (_: React.SyntheticEvent, newValue: SkinnyMeetingDefinition | null) => {
      if (newValue?.id === -1 && onCreateNewTemplate) {
        onCreateNewTemplate();
        return;
      }
      onChange(newValue);
    },
    [onCreateNewTemplate, onChange],
  );

  const getTemplateIcon = useCallback((inviteStyle: string) => {
    switch (inviteStyle) {
      case INVITE_STYLE.CALENDAR_FIRST:
        return <CalendarMonth sx={{ fontSize: 16, mr: 1 }} />;
      case INVITE_STYLE.CUSTOM_INVITE:
        return <MailOutline sx={{ fontSize: 16, mr: 1 }} />;
      default:
        return null;
    }
  }, []);

  const handleInputChange = useCallback(
    (_: React.SyntheticEvent, v: string) => {
      setInput(v);
    },
    [],
  );

  const isOptionEqualToValue = useCallback(
    (option: SkinnyMeetingDefinition, value: SkinnyMeetingDefinition) => {
      return option.id === value.id;
    },
    [],
  );

  const getOptionLabel = useCallback((option: SkinnyMeetingDefinition) => {
    if (option.id === -1) return "";
    return option.name;
  }, []);

  return (
    <Box sx={{ width: "100%" }}>
      <Autocomplete
        fullWidth
        disabled={disabled}
        onInputChange={handleInputChange}
        onChange={handleChange}
        value={value ?? undefined}
        isOptionEqualToValue={isOptionEqualToValue}
        getOptionLabel={getOptionLabel}
        disableListWrap
        disableClearable
        filterOptions={(options) => options}
        componentsProps={{
          popper: {
            style: {
              width: "fit-content",
              zIndex: 1300,
            },
            placement: "bottom-start",
          },
          paper: {
            style: {
              maxHeight: "300px",
              overflow: "auto",
              marginTop: "4px",
            },
          },
        }}
        renderOption={useCallback(
          (
            props: React.HTMLAttributes<HTMLLIElement>,
            option: SkinnyMeetingDefinition,
          ) => {
            if (option.id === -1) {
              return (
                <ListItem key={option.id} {...props} dense>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      width: "100%",
                    }}
                  >
                    <Add sx={{ fontSize: 16, mr: 1, color: "primary.main" }} />
                    <Typography
                      sx={{ fontWeight: "bold", color: "primary.main" }}
                    >
                      Create New Template
                    </Typography>
                  </Box>
                </ListItem>
              );
            }

            return (
              <ListItem key={option.id} {...props} dense>
                <Box
                  sx={{ display: "flex", alignItems: "center", width: "100%" }}
                >
                  {getTemplateIcon(option.inviteStyle)}
                  <ListItemText
                    primary={option.name}
                    secondary={!option.active ? "Inactive" : undefined}
                  />
                </Box>
              </ListItem>
            );
          },
          [getTemplateIcon],
        )}
        options={options}
        renderInput={(params) => (
          <TextField
            variant="outlined"
            {...params}
            label="Assign Template"
            error={!!error}
            helperText={
              error ? "Error loading templates. Please try again." : undefined
            }
            InputProps={{
              ...params.InputProps,
              endAdornment: <>{params.InputProps.endAdornment}</>,
            }}
            sx={{
              "& .MuiInputBase-root": {
                position: "relative",
              },
            }}
          />
        )}
      />
    </Box>
  );
}

export default FollowUpTemplateDropdown;
