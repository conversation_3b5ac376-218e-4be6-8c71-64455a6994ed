import React from "react";
import { Checkbox } from "@mui/material";

import { Table, Column, TableProps } from "./Table";

export function SelectTable<
  T extends {
    id: number | string;
  },
>({
  loading = false,
  minRows = 0,
  columns = [],
  data = [],
  sort,
  onSortChange = () => null,
  selected,
  onSelect,
  getDisabled,
}: {
  selected: (number | string)[];
  onSelect: (checked: boolean, selected: T[]) => void;
  getDisabled?: (item: T) => boolean;
} & TableProps<T>) {
  // Filter out disabled items when calculating selection state
  const selectableData = data.filter((item) => !getDisabled?.(item));

  const allSelected =
    selectableData.length > 0 &&
    selectableData.every((c) => selected.includes(c.id));

  const indeterminate =
    !allSelected &&
    selected.length > 0 &&
    selectableData.some((c) => selected.includes(c.id));

  return (
    <Table
      getKey={(i) => i.id}
      loading={loading}
      columns={[
        {
          component: (c) => {
            const isDisabled = getDisabled?.(c) || false;
            const isSelected = selected.includes(c.id);

            return (
              <Checkbox
                color="primary"
                checked={isSelected || isDisabled}
                disabled={isDisabled}
                onChange={() => {
                  if (!isDisabled) {
                    onSelect(isSelected, [c]);
                  }
                }}
              />
            );
          },
          id: "checkbox",
          label: (
            <Checkbox
              color="primary"
              indeterminate={indeterminate}
              checked={allSelected}
              onChange={() => {
                if (allSelected) {
                  onSelect(true, selectableData);
                } else if (indeterminate) {
                  onSelect(
                    false,
                    selectableData.filter((c) => !selected.includes(c.id)),
                  );
                } else {
                  onSelect(false, selectableData);
                }
              }}
            />
          ),
          width: 50,
        } as Column<T>,
      ].concat(columns)}
      data={data}
      minRows={minRows}
      sort={sort}
      onSortChange={onSortChange}
      onRowClick={(c) => {
        if (!getDisabled?.(c)) {
          onSelect(selected.includes(c.id), [c]);
        }
      }}
    />
  );
}
