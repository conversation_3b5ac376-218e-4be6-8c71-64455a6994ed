import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Stack,
  Step,
  StepButton,
  Stepper,
  Switch,
  TextField,
  Typography,
} from "@mui/material";
import { Delete, Error as ErrorIcon } from "@mui/icons-material";
import { useEffect, useState } from "react";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { Redirect, useHistory, useParams } from "react-router-dom";
import { TemplateEditDistribution } from "src/componentsV2/TemplateEditDistribution";
import { TemplateEditInviteAndEmail } from "src/componentsV2/TemplateEditInviteAndEmail";
import { TemplateEditSettings } from "src/componentsV2/TemplateEditSettings";
import { TemplateEditTimeAttributes } from "src/componentsV2/TemplateEditTimeAttributes";
import PrimaryButton from "src/componentsV2/buttons/PrimaryButton";
import SecondaryButton from "src/componentsV2/buttons/SecondaryButton";
import useGeneralNotifications from "src/hooks/useGeneralNotifications";
import useUrlQueryV2 from "src/hooks/useUrlQueryV2";
import { useActivateTemplate, useDeactivateTemplate } from "src/mutations";
import { useDeleteMeetingTemplate } from "src/mutations/useDeleteMeetingTemplate";
import {
  MeetingTemplateUpdateBody,
  useUpdateMeetingTemplate,
} from "src/mutations/useUpdateMeetingTemplate";
import { useUpdateRoutingLogicUser } from "src/mutations/useUpdateRoutingLogicUser";
import { useMeetingDefinition } from "src/queries";
import { RoutingLogic, useRoutingLogic } from "src/queries/useRoutingLogic";
import { formatDateTime } from "src/services/formatDateTime";
import { MeetingDefinition } from "src/types";
import { ROLE_LEVELS } from "../../../auth/roles";
import { formatRoutingLogic } from "src/utils/formatRoutingLogic";
import {
  useActingAs,
  useActingAsOverrideHeader,
  useIsDefaultUser,
} from "src/auth";
import { useTokenRefreshHandler } from "src/hooks";
import { useUserService } from "src/services";
import { getUserDetails, getUserToken } from "src/utils/jwtToken";
import { errorHandler } from "src/hooks/errorHandler";
import { isWebinar } from "src/meetingTypes/invite/props";
import WebinarEditTimeAttributesAndInvite from "src/componentsV2/WebinarEditTimeAttributesAndInvite";
import WebinarEditPromote from "src/componentsV2/WebinarEditPromote";
import WebinarEditInviteGuests from "src/componentsV2/WebinarEditInviteGuests";
import dayjs from "dayjs";

export default function MeetingTemplateEditPage() {
  const [activeStep, setActiveStep] = useState(0);
  const [showValidationDialog, setShowValidationDialog] = useState(false);
  const { addGeneralNotification, addError } = useGeneralNotifications();

  const { id }: { id: string } = useParams();
  const { data: routingLogic } = useRoutingLogic(id);

  const query = useUrlQueryV2();
  const history = useHistory();
  const [disableSave, setDisableSave] = useState(false);
  const [hideMeetingHosts, setHideMeetingHosts] = useState(false);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [actionInProgress, setActionInProgress] = useState(false);
  const deleteMeetingTemplate = useDeleteMeetingTemplate();
  const activateTemplate = useActivateTemplate();
  const deactivateTemplate = useDeactivateTemplate();
  const updateRoutingLogic = useUpdateRoutingLogicUser();
  const isDefaultUser = useIsDefaultUser();

  const methods = useForm<MeetingDefinition>({
    mode: "all",
  });
  const {
    register,
    handleSubmit,
    control,
    getValues,
    setValue,
    reset,
    formState: { errors },
    watch,
  } = methods;

  const updateMeetingTemplate = useUpdateMeetingTemplate();
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);

  const { data: meetingDefinition, error } = useMeetingDefinition(id);

  const userDetails = getUserDetails();
  const actingAsDetails = useActingAs();

  // ---------------- Default routing logic fetching.
  // We have to do all of this here, because we need to invoke this API call on team change, and wait for the result.
  const accessToken = getUserToken();
  const service = useUserService();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const fetchDefaultRoutingLogic = (teamId: number | string) =>
    service
      .get(`/api/meetings/definition/default_routing_logic/${teamId}`)
      .set(headers)
      .then(tokenRefreshHandler)
      .then((res: Response) => res.body)
      .then((data?: RoutingLogic[]) => data)
      .catch(errorHandler);

  // ---------------- Default routing logic fetching END.

  useEffect(() => {
    setActiveStep(Number(query.get("step") || 0));
  }, [query]);

  useEffect(() => {
    if (!meetingDefinition) {
      return;
    }

    // If the user is the default user, and they haven't created the template, they shouldn't be able to edit it.
    let ableToEdit = true;

    if (actingAsDetails[0]?.role === ROLE_LEVELS.DEFAULT) {
      ableToEdit =
        actingAsDetails[0].id ===
        meetingDefinition?.creationData.creatorData.userId;
    } else if (userDetails?.role === ROLE_LEVELS.DEFAULT) {
      ableToEdit =
        userDetails?.id === meetingDefinition?.creationData.creatorData.userId;
    }

    if (!ableToEdit) {
      history.replace("/meeting-templates");
    }

    if (
      meetingDefinition.routing_logic?.metadata?.subtypeDetails.subtype ===
      "email"
    ) {
      meetingDefinition.routing = "email";
    }

    reset(meetingDefinition);
    setInitialDataLoaded(true);
  }, [meetingDefinition, reset]);

  if (error) {
    console.error(error);
    return <Redirect to="/404" />;
  }

  const createdByDefaultUser =
    meetingDefinition?.creationData.creatorData.userRole ===
    ROLE_LEVELS.DEFAULT;

  if (!initialDataLoaded) {
    return (
      <Stack
        sx={{
          width: "100%",
          height: "100%",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <CircularProgress size="10vh" />
      </Stack>
    );
  }



  const onSubmit = async (
    data: MeetingDefinition,
    _: React.BaseSyntheticEvent<object, any, any> | undefined,
    newRoutingLogic?: RoutingLogic[],
    showNotification = false,
  ) => {
    setDisableSave(true);
    try {
      const templateUpdateBody = {
        ...data,
        enabled: data.active,
        team: data.team.id,
        routingJustMe: data.routingJustMe,
        tags: data.tags.map((tag) => tag.id),
      };

      if (!(isDefaultUser || createdByDefaultUser)) {
        templateUpdateBody.routing_logic = formatRoutingLogic(
          templateUpdateBody,
          data,
          newRoutingLogic,
          routingLogic,
        );
      } else {
        // Reset routing logic for personal meeting type.
        templateUpdateBody.routing = "sequential";
        templateUpdateBody.routing_logic = {
          sequential: {
            last_processed: 0,
            order: null,
          },
          custom: {},
        };
      }

      if (data.inviteStyle === "link_first") {
        // For "link_first" we set hardcoded 1-30 day range since this
        // meeting type does not allow day range configuration and
        // default value is not good.
        templateUpdateBody.properties.dayRange.from = 1;
        templateUpdateBody.properties.dayRange.to = 30;
      }

      await updateMeetingTemplate(data.id, templateUpdateBody);
      if (showNotification) {
        addGeneralNotification(
          `Template ${templateUpdateBody.name} updated successfully!`,
        );
      }
    } catch (error) {
      addError(`Template update failed: ${getErrorMessage(error)}`);
      console.error(error);
    } finally {
      setDisableSave(false);
    }
  };
  return (
    <Box sx={{ backgroundColor: "#f1f1f1", minHeight: "100%", pt: 4 }}>
      <Container maxWidth="xl" disableGutters sx={{ px: "30px", pb: "65px" }}>
        <Dialog
          open={showValidationDialog}
          onClose={() => setShowValidationDialog(false)}
        >
          <DialogTitle>Invalid Settings</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {activeStep === 1
                ? "Please select a future time for your event. The current time must be after the current date and time."
                : "Please ensure all email templates have valid timing settings. Each email must have:\n- A positive number\n- A time unit selected (minutes, hours, days, or weeks)"}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <PrimaryButton onClick={() => setShowValidationDialog(false)}>
              OK
            </PrimaryButton>
          </DialogActions>
        </Dialog>
        <Dialog
          open={deleteDialogVisible}
          onClose={() => setDeleteDialogVisible(false)}
        >
          <DialogTitle>
            <Typography
              sx={{ color: "primary.dark", fontWeight: "bold" }}
              variant="h5"
              component="span"
            >
              Delete Template
            </Typography>
          </DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete the template?
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ justifyContent: "flex-start", px: 3 }}>
            <SecondaryButton
              disabled={actionInProgress}
              onClick={() => setDeleteDialogVisible(false)}
            >
              Cancel
            </SecondaryButton>
            <PrimaryButton
              disabled={actionInProgress}
              onClick={async () => {
                if (!meetingDefinition?.id) {
                  setDeleteDialogVisible(false);
                  return;
                }
                try {
                  setActionInProgress(true);
                  await deleteMeetingTemplate(meetingDefinition.id);

                  history.push("/meeting-templates");
                  addGeneralNotification(
                    `Succesfully deleted template ${meetingDefinition.name}`,
                  );
                  setDeleteDialogVisible(false);
                } catch (error) {
                  addError(
                    `Failed to delete the template: ${getErrorMessage(error)}`,
                  );
                  console.error(error);
                } finally {
                  setActionInProgress(false);
                }
              }}
            >
              Delete
            </PrimaryButton>
          </DialogActions>
        </Dialog>

        <FormProvider {...methods}>
          <Card sx={{ mb: 4 }}>
            <CardContent>
              <Stack
                sx={{
                  mb: 3,
                  flexDirection: "row",
                  gap: 4,
                  alignItems: "center",
                }}
              >
                <Stack>
                  <TextField
                    {...register("name", {
                      required: "Template name is required.",
                    })}
                    InputProps={{
                      sx: { fontWeight: "bold", fontSize: "22px" },
                    }}
                    sx={{ width: "400px" }}
                    variant="standard"
                    placeholder="New Template Name"
                    label="Template name"
                  />
                  {errors.name && (
                    <Typography sx={{ color: "red" }}>
                      {errors.name.message}
                    </Typography>
                  )}
                </Stack>
                <Box>
                  <Typography fontSize={"14px"}>
                    Created By:{" "}
                    {`${getValues(
                      "creationData.creatorData.userFirstName",
                    )} ${getValues("creationData.creatorData.userLastName")}`}
                  </Typography>
                  <Typography fontSize={"14px"}>
                    Created At:{" "}
                    {formatDateTime(getValues("creationData.createdAt"))}
                  </Typography>
                </Box>
                <Stack
                  sx={{
                    flexDirection: "row",
                    alignItems: "center",
                    marginLeft: "auto",
                    gap: 2,
                  }}
                >
                  <IconButton
                    sx={{ borderRadius: "9999px", backgroundColor: "#E1E6EB" }}
                    aria-label="delete"
                    size="small"
                    onClick={() => setDeleteDialogVisible(true)}
                  >
                    <Delete fontSize="small" />
                  </IconButton>
                  <Controller
                    control={control}
                    name="active"
                    render={({ field }) => (
                      <Switch
                        {...field}
                        checked={field.value}
                        disabled={disableSave}
                        onChange={async (event) => {
                          setDisableSave(true);

                          try {
                            field.onChange(event.target.checked);

                            if (event.target.checked) {
                              await activateTemplate(getValues("id"));
                              addGeneralNotification(
                                `Template ${getValues(
                                  "name",
                                )} successfully activated!`,
                              );
                            } else {
                              await deactivateTemplate(getValues("id"));
                              addGeneralNotification(
                                `Template ${getValues(
                                  "name",
                                )} successfully deactivated!`,
                              );
                            }
                          } catch (error) {
                            addError(
                              `Template activation failed: ${getErrorMessage(
                                error,
                              )}`,
                            );

                            field.onChange(!event.target.checked);
                          } finally {
                            setDisableSave(false);
                          }
                        }}
                      />
                    )}
                  />
                  <PrimaryButton
                    // Forms isValid flag is not working properly so had to do it like this.
                    disabled={Object.keys(errors).length > 0 || disableSave}
                    onClick={() => {
                      handleSubmit((data, event) =>
                        onSubmit(data, event, undefined, true),
                      )();
                    }}
                  >
                    Save
                  </PrimaryButton>
                </Stack>
              </Stack>

              {/* non-webinar steps */}
              {!isWebinar(watch("inviteStyle")) && (
                <Stepper nonLinear activeStep={activeStep}>
                  <Step>
                    <StepButton
                      color="inherit"
                      onClick={() =>
                        history.push({
                          search: `?step=0`,
                        })
                      }
                    >
                      Template Settings
                    </StepButton>
                  </Step>
                  <Step>
                    <StepButton
                      icon={
                        (errors.emailTemplates ||
                          errors.inviteTemplates ||
                          errors.properties?.cleanDeclineRule ||
                          errors.properties?.meetingReminder) && (
                          <ErrorIcon sx={{ color: "red" }} />
                        )
                      }
                      color="inherit"
                      onClick={() =>
                        history.push({
                          search: `?step=1`,
                        })
                      }
                    >
                      Email & Invite
                    </StepButton>
                  </Step>
                  <Step>
                    <StepButton
                      color="inherit"
                      onClick={() =>
                        history.push({
                          search: `?step=2`,
                        })
                      }
                    >
                      Meeting Distribution
                    </StepButton>
                  </Step>
                  <Step>
                    <StepButton
                      color="inherit"
                      onClick={() =>
                        history.push({
                          search: `?step=3`,
                        })
                      }
                    >
                      Time Attributes
                    </StepButton>
                  </Step>
                </Stepper>
              )}

              {/* webinar steps */}
              {isWebinar(watch("inviteStyle")) && (
                <Stepper nonLinear activeStep={activeStep}>
                  <Step>
                    <StepButton
                      color="inherit"
                      onClick={() =>
                        history.push({
                          search: `?step=0`,
                        })
                      }
                    >
                      Template Settings
                    </StepButton>
                  </Step>
                  <Step>
                    <StepButton
                      icon={
                        errors.inviteTemplates && (
                          <ErrorIcon sx={{ color: "red" }} />
                        )
                      }
                      color="inherit"
                      onClick={() =>
                        history.push({
                          search: `?step=1`,
                        })
                      }
                    >
                      Event Setup
                    </StepButton>
                  </Step>
                  <Step>
                    <StepButton
                      color="inherit"
                      onClick={() =>
                        history.push({
                          search: `?step=2`,
                        })
                      }
                    >
                      Event Promotion
                    </StepButton>
                  </Step>
                  <Step>
                    <StepButton
                      color="inherit"
                      onClick={() =>
                        history.push({
                          search: `?step=3`,
                        })
                      }
                    >
                      Invite Guests
                    </StepButton>
                  </Step>
                </Stepper>
              )}
            </CardContent>
          </Card>
          <Card>
            {/* non-webinar flow */}
            {!isWebinar(watch("inviteStyle")) && (
              <CardContent>
                {/* We have to show/hide the steps through css, otherwise the validation doesn't work properly, because the elements are not present on the page */}
                <Box sx={{ display: activeStep === 0 ? "block" : "none" }}>
                  <Box sx={{ mb: 5 }}>
                    <TemplateEditSettings />
                  </Box>
                  <PrimaryButton
                    onClick={() => {
                      history.push({
                        search: `?step=${activeStep + 1}`,
                      });
                      window.scrollTo(0, 0);
                    }}
                  >
                    Next
                  </PrimaryButton>
                </Box>
                <Box sx={{ display: activeStep === 1 ? "block" : "none" }}>
                  <Box sx={{ mb: 5 }}>
                    <TemplateEditInviteAndEmail />
                  </Box>
                  <Stack sx={{ flexDirection: "row", gap: 2 }}>
                    <SecondaryButton
                      onClick={() => {
                        history.push({
                          search: `?step=${activeStep - 1}`,
                        });
                        window.scrollTo(0, 0);
                      }}
                    >
                      Previous
                    </SecondaryButton>
                    <PrimaryButton
                      onClick={async () => {
                        // If save successful, proceed to next step
                        history.push({
                          search: `?step=${activeStep + 1}`,
                        });
                        window.scrollTo(0, 0);
                      }}
                    >
                      Next
                    </PrimaryButton>
                  </Stack>
                </Box>
                <Box sx={{ display: activeStep === 2 ? "block" : "none" }}>
                  <Box sx={{ mb: 5 }}>
                    <TemplateEditDistribution
                      hideMeetingHosts={hideMeetingHosts}
                      currentTeam={meetingDefinition?.team}
                      routingLogic={routingLogic}
                      createdByDefaultUser={createdByDefaultUser}
                      onTeamChange={async () => {
                        try {
                          setDisableSave(false);
                          setHideMeetingHosts(false);
                          const newRoutingLogic =
                            await fetchDefaultRoutingLogic(
                              getValues("team.id"),
                            );

                          // When changing teams, always default to the "sequential" routing logic first.
                          setValue("routing", "sequential");
                          handleSubmit((data, event) =>
                            onSubmit(data, event, newRoutingLogic),
                          )();
                        } catch (error: any) {
                          if (error?.status === 404 && error.message) {
                            addError(JSON.parse(error.message).message);
                            setDisableSave(true);
                            setHideMeetingHosts(true);
                          }
                        }
                      }}
                      // here - need to handle error here
                      onUpdateRoutingLogicUser={(routingLogic: RoutingLogic) =>
                        updateRoutingLogic(id, routingLogic.user_id, {
                          enabled: routingLogic.enabled,
                          mapping_logic: routingLogic.mapping_logic,
                        })
                      }
                    />
                  </Box>
                  <Stack sx={{ flexDirection: "row", gap: 2 }}>
                    <SecondaryButton
                      onClick={() => {
                        history.push({
                          search: `?step=${activeStep - 1}`,
                        });
                        window.scrollTo(0, 0);
                      }}
                    >
                      Previous
                    </SecondaryButton>
                    <PrimaryButton
                      onClick={async () => {
                        // Save template first
                        try {
                          setDisableSave(true);
                          await handleSubmit((data, event) =>
                            onSubmit(data, event, undefined, false),
                          )();

                          // If save successful, proceed to next step
                          history.push({
                            search: `?step=${activeStep + 1}`,
                          });
                          window.scrollTo(0, 0);
                        } catch (error) {
                          addError(
                            `Failed to save template: ${getErrorMessage(error)}`,
                          );
                        } finally {
                          setDisableSave(false);
                        }
                      }}
                    >
                      Next
                    </PrimaryButton>
                  </Stack>
                </Box>
                <Box sx={{ display: activeStep === 3 ? "block" : "none" }}>
                  <Box sx={{ mb: 5 }}>
                    <TemplateEditTimeAttributes
                      meetingDefinitionId={meetingDefinition?.id}
                    />
                  </Box>
                  <SecondaryButton
                    onClick={() => {
                      history.push({
                        search: `?step=${activeStep - 1}`,
                      });
                      window.scrollTo(0, 0);
                    }}
                  >
                    Previous
                  </SecondaryButton>
                </Box>
              </CardContent>
            )}

            {/* webinar flow */}
            {isWebinar(watch("inviteStyle")) && (
              <CardContent>
                {/* We have to show/hide the steps through css, otherwise the validation doesn't work properly, because the elements are not present on the page */}
                <Box sx={{ display: activeStep === 0 ? "block" : "none" }}>
                  <Box sx={{ mb: 5 }}>
                    <TemplateEditSettings />
                  </Box>
                  <PrimaryButton
                    onClick={() => {
                      history.push({
                        search: `?step=${activeStep + 1}`,
                      });
                      window.scrollTo(0, 0);
                    }}
                  >
                    Next
                  </PrimaryButton>
                </Box>
                <Box sx={{ display: activeStep === 1 ? "block" : "none" }}>
                  <Box sx={{ mb: 5 }}>
                    <WebinarEditTimeAttributesAndInvite />
                  </Box>
                  <Stack sx={{ flexDirection: "row", gap: 2 }}>
                    <SecondaryButton
                      onClick={() => {
                        history.push({
                          search: `?step=${activeStep - 1}`,
                        });
                        window.scrollTo(0, 0);
                      }}
                    >
                      Previous
                    </SecondaryButton>
                    <PrimaryButton
                      onClick={async () => {
                        const formData = methods.getValues();
                        const startTime = formData.webinarConfig?.startTime;

                        if (startTime) {
                          const now = dayjs();
                          const selectedTime = dayjs(startTime);
                          if (selectedTime.isBefore(now, "minute")) {
                            setShowValidationDialog(true);
                            return;
                          }
                        }

                        history.push({
                          search: `?step=${activeStep + 1}`,
                        });
                        window.scrollTo(0, 0);
                      }}
                    >
                      Next
                    </PrimaryButton>
                  </Stack>
                </Box>

                <Box sx={{ display: activeStep === 2 ? "block" : "none" }}>
                  <Box sx={{ mb: 5 }}>
                    <WebinarEditPromote />
                  </Box>
                  <Stack sx={{ flexDirection: "row", gap: 2 }}>
                    <SecondaryButton
                      onClick={() => {
                        history.push({
                          search: `?step=${activeStep - 1}`,
                        });
                        window.scrollTo(0, 0);
                      }}
                    >
                      Previous
                    </SecondaryButton>
                    <PrimaryButton
                      onClick={async () => {
                        const formData = methods.getValues();
                        const emailTemplates = formData.emailTemplates || [];

                        // Validate email template timing settings
                        const hasInvalidTiming = emailTemplates.some(
                          (template) =>
                            !template.sendMessageTiming?.num ||
                            !template.sendMessageTiming?.units ||
                            template.sendMessageTiming.num < 0,
                        );

                        if (hasInvalidTiming) {
                          setShowValidationDialog(true);
                          return;
                        }

                        // Save template first
                        try {
                          setDisableSave(true);
                          await handleSubmit((data, event) =>
                            onSubmit(data, event, undefined, false),
                          )();

                          // If save successful, proceed to next step
                          history.push({
                            search: `?step=${activeStep + 1}`,
                          });
                          window.scrollTo(0, 0);
                        } catch (error) {
                          addError(
                            `Failed to save template: ${getErrorMessage(error)}`,
                          );
                        } finally {
                          setDisableSave(false);
                        }
                      }}
                    >
                      Next
                    </PrimaryButton>
                  </Stack>
                </Box>
                <Box sx={{ display: activeStep === 3 ? "block" : "none" }}>
                  <Box sx={{ mb: 5 }}>
                    <WebinarEditInviteGuests
                      meetingTemplate={meetingDefinition}
                      setDisableSave={setDisableSave}
                    />
                  </Box>
                  <SecondaryButton
                    onClick={() => {
                      history.push({
                        search: `?step=${activeStep - 1}`,
                      });
                      window.scrollTo(0, 0);
                    }}
                  >
                    Previous
                  </SecondaryButton>
                </Box>
              </CardContent>
            )}
          </Card>
        </FormProvider>
      </Container>
    </Box>
  );
}

const getErrorMessage = (error: any) => {
  let errorMessage = "Unknown error";

  if (error?.response?.body?.errors?.[0]) return error.response.body.errors[0];
  if (error?.response?.body?.message) return error.response.body.message;

  if (error instanceof Error && error.message) {
    errorMessage = error.message;
  }

  if (error instanceof Error && JSON.parse(error.message)?.message) {
    errorMessage = JSON.parse(error.message).message;
  }

  // Sometimes the BE returns the errors as an array, so we show the first one.
  if (error instanceof Error && JSON.parse(error.message)?.errors?.[0]) {
    errorMessage = JSON.parse(error.message).errors[0];
  }

  return errorMessage;
};
