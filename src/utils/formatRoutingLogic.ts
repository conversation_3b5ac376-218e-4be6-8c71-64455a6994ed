import { MeetingDefinition } from "../types";
import { RoutingLogic } from "src/queries/useRoutingLogic";
import { MeetingTemplateUpdateBody } from "src/mutations/useUpdateMeetingTemplate";

export const formatRoutingLogic = (
  templateUpdateBody: MeetingTemplateUpdateBody,
  formData: MeetingDefinition,
  newRoutingLogic?: RoutingLogic[],
  routingLogic?: RoutingLogic[],
) => {
  // We have to keep the code below for now until we get a new endpoint for updating Meeting Templates.
  // If the team has changed, newRoutingLogic is passed in as a third param, and we need to use it to reset the sequential props, in the routing_logic prop.
  if (newRoutingLogic) {
    if (templateUpdateBody.routing_logic) {
      templateUpdateBody.routing_logic.sequential.order = newRoutingLogic.map(
        (routingLogicItem) => routingLogicItem.user_id,
      );

      templateUpdateBody.routing_logic.custom = {};
      newRoutingLogic.forEach((routingLogicItem) => {
        if (templateUpdateBody.routing_logic) {
          templateUpdateBody.routing_logic.custom[routingLogicItem.user_id.toString()] =
            {
              enabled: routingLogicItem.enabled,
              mapping_logic: routingLogicItem.mapping_logic || [],
            };
        }
      });
    }

    delete templateUpdateBody.routing_logic?.metadata;

    // If newRoutingLogic isn't passed in, we need to populate the routing_logic properties with the data from the routing logic endpoint. We populate these differently, depending on if the user selected the "email" or "sequential" routing logic.
  } else if (formData.routing === "email") {
    if (templateUpdateBody.routing_logic) {
      const subtypeDetailsField =
        templateUpdateBody.routing_logic.metadata?.subtypeDetails.field ??
        "routing_field";

      templateUpdateBody.routing = "custom";
      templateUpdateBody.routing_logic.metadata = {
        subtypeDetails: {
          field: subtypeDetailsField,
          subtype: "email",
        },
      };

      routingLogic?.forEach((item) => {
        if (templateUpdateBody.routing_logic) {
          templateUpdateBody.routing_logic.custom[item.user_id.toString()] = {
            enabled: item.enabled,
            mapping_logic: [
              {
                field: subtypeDetailsField,
                operator: "is",
                value: item.email,
              },
            ],
          };
        }
      });
    }
  } else if (formData.routing === "sequential") {
    delete templateUpdateBody.routing_logic?.metadata;

    // For both the sequential and custom routing logic we have to set the `enabled` flag.
    // We do not touch the sequential property, just this one in both cases.
    routingLogic?.forEach((item) => {
      if (templateUpdateBody.routing_logic) {
        templateUpdateBody.routing_logic.custom[item.user_id.toString()] = {
          enabled: item.enabled,
          mapping_logic: [],
        };
      }
    });
  }

  // We have to delete this because otherwise the patch fails.
  if (templateUpdateBody.routing_logic?.sequential) {
    // @ts-ignore - We need to delete this property for the API
    delete templateUpdateBody.routing_logic.sequential.last_processed;
  }

  return templateUpdateBody.routing_logic;
};
